<html>
    <head>
        <title>6-24</title>
    </head>

    <body>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>" enctype="multipart/form-data">
            <h1>Please provide the following documents:</h1>
            Select an image to upload:<input type="file" name="myimage"><br/>
            Select a PDF file to upload: <input type="file" name="mypdf"><br/>
            <br/>
            <input type="submit" name="submit" value="submit"><br/>
        </form>

        <?php
            function uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed)
            {
                $uploadOk = 1;
                $dir = "upload/";

                $file = $dir . basename($_FILES[$tagName]['name']) ;
                $fileType = pathinfo($file, PATHINFO_EXTENSION);
                $fileSize = $_FILES[$tagName]['size'];

                if ($fileSize > $sizeAllowed)
                {
                    $uploadOk = 0;
                    echo "File is too large! <br>";
                }

                if (!stristr($fileAllowed, $fileType))
                {
                    $uploadOk = 0;
                    echo "File type is not allowed! <br>";
                }

                if (!$overwriteAllowed && file_exists($file))
                {
                    $uploadOk = 0;
                    echo "File already exists! <br>";
                }

                if ($uploadOk == 1)
                {
                    if (move_uploaded_file($_FILES[$tagName]["tmp_name"] , $file))
                        return $file;
                    else
                        return false;
                }
                else
                    return false;
            }
        ?>

        <?php
            echo "<hr>";

            if (isset($_POST["submit"]))
            {
                $tagName = "myimage";
                $fileAllowed = "PNG:JPEG:JPG:GIF:BMP";
                $sizeAllowed = 5000000; // 5MB
                $overwriteAllowed = 1;
                $file = uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed);

                if ($file != false)
                {
                    echo "<img src='".$file."' width=200 height=200> <br><br>";
                }
                else
                    echo "Submission of the image failed! <br>";

                // for PDF file
                $tagName = "mypdf";
                $fileAllowed = "PDF";
                $sizeAllowed = 6000000; // 6MB
                $overwriteAllowed = 1;
                $file = uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed);

                if ($file != false)
                {
                    echo "Click <a href='".$file."'>here</a> to see my CV <br>";
                    echo "<embed type='application/pdf' src='".$file."' width=500 height=500>";
                }
                else
                    echo "Submission of the PDF failed! <br>";
            }
        ?>
    </body>
</html>
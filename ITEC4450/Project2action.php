<html>
    <head>
        <title>Project 2</title>
    </head>

    <body>
        <?php
            function get_input($what, $from) {
                if (array_key_exists($what, $from))
                    return $from[$what];
                else
                    return null;
            }

            function clean_input($input) {
                if ($input == null)
                    return null;
                $input = trim($input);
                $input = htmlspecialchars($input);
                return $input;
            }

            if (get_input("submitme", $_POST) != null) {
                $admin_name = clean_input(get_input("admin_name", $_POST));
                $admin_passwd = clean_input(get_input("passwd", $_POST));

                $valid_admin = "admin";
                $valid_passwd = "admin";

                if (empty($admin_name) || empty($admin_passwd)) {
                    echo "Please enter both Administrator ID and Password.";
                } else if ($admin_name == $valid_admin && $admin_passwd == $valid_passwd) {
                    $file = "test_results.txt";

                    if (file_exists($file))
                    {
                        $infoStr = file_get_contents($file);
                        $infoStr = trim($infoStr);
                        $infoList = explode("\n", $infoStr);
                        $personInfo = array();

                        foreach ($infoList as $index => $line) {
                            $personInfo[$index] = explode("\t", $line);
                        }

                        $showwhat = clean_input(get_input("showwhat", $_POST));
                        $student_name = clean_input(get_input("student", $_POST));

                        if ($showwhat == "byname" && empty($student_name)) {
                            echo "Please enter a student name to search for.";
                            exit;
                        }

                        if ($showwhat == "all") {
                            echo "The grade for each student is shown as follows. <br>";
                        } else if ($showwhat == "p100") {
                            echo "The students who got 100 points. <br>";
                        } else if ($showwhat == "dm0") {
                            echo "The Digital Media students who got 0 points. <br>";
                        } else if ($showwhat == "bydate") {
                            echo "The info for students who took the test after 8:00am of yesterday. <br>";
                        } else if ($showwhat == "byname") {
                            echo "The info for students whose name is: " . $student_name . " <br>";
                        }

                        echo "<table border='1'>";
                        echo "<tr>
                            <th>No.</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Major</th>
                            <th>Grade</th>
                            <th>IP Address</th>
                            <th>Date</th>
                            <th>Time</th>
                        </tr>";

                        $found = 0;
                        foreach ($personInfo as $index => $person) {
                            $display = false;

                            if ($showwhat == "all") {
                                $display = true;
                            } else if ($showwhat == "p100" && $person[3] == "100") {
                                $display = true;
                            } else if ($showwhat == "dm0" && $person[3] == "0" && $person[2] == "Digital Media") {
                                $display = true;
                            } else if ($showwhat == "bydate") {
                                $test_date = strtotime($person[5] . " " . $person[6]);
                                $yesterday = strtotime("yesterday 8:00am");
                                if ($test_date > $yesterday) {
                                    $display = true;
                                }
                            } else if ($showwhat == "byname" && stripos($person[0], $student_name) !== false) {
                                $display = true;
                            }
                            if ($display) {
                                $found++;
                                echo "<tr>";
                                echo "<td>".$found."</td>";
                                foreach ($person as $info) {
                                    echo "<td>".$info."</td>";
                                }
                                echo "</tr>";
                            }
                        }
                        echo "</table>";

                        echo "Total ".$found." records found.";
                    } else {
                        echo "No test results available.";
                    }
                }
            }
        ?>
    </body>
</html>
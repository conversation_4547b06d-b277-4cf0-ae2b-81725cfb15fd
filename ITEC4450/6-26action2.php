<?php
    session_start();
?>

<html>
    <head>
        <title>6-26</title>
    </head>

    <body>
        <?php
            error_reporting(E_ERROR | E_PARSE); // will only display fatal errors and parsing errors, suppressing warnings and notices

            echo "Thank you for shopping at GGC Store! <br>";

            if ($_SESSION["buy"] != null)
            {
                echo "Totally you want to buy ".count($_SESSION["buy"])." items. And they are: ";
                echo "<ul>";
                foreach ($_SESSION["buy"] as $item)
                    echo "<li>".$item[3]." &times; ".$item[0]."</li>";
                echo "</ul>";

                echo "Please pay $".$_SESSION["totalCost"]."<br>";
            }

            session_unset();
            session_destroy();

            echo "Your transaction is completed and please click <a href='6-26.php'>here</a> to continue shopping. <br>";
        ?>
    </body>
</html>
<html>
    <head>
        <title>6-24</title>
    </head>

    <body>
        <?php
            function displayArray($A)
            {
                foreach ($A as $v)
                    echo $v."\t";
                echo "<br>";
            }

            $numbers = array(14, 5, 19, 100, -11, -4.3, 9.2, -24, 0, 45);
            echo "Original Data: <br>";
            displayArray($numbers);

            echo "Sorting in ascending <br>";
            sort($numbers);
            displayArray($numbers);

            echo "Sorting in descending <br>";
            rsort($numbers);
            displayArray($numbers);
        ?>

        <?php
            echo "<hr>";

            function displayAssociative($A)
            {
                foreach ($A as $key=> $value)
                    echo $key."=>".$value."<br>";
                echo "<br>";
            }

            $people = array("Peter"=>35, "Ben"=>27, "Joe"=>18, "<PERSON>"=>46);
            echo "Original Data: <br>";
            displayAssociative($people);

            echo "Sorting in ascending for associative array by values <br>";
            asort($people);
            displayAssociative($people);

            echo "Sorting in descending for associative array by values <br>";
            arsort($people);
            displayAssociative($people);

            echo "Sorting in ascending for associative array by keys <br>";
            ksort($people);
            displayAssociative($people);

            echo "Sorting in descending for associative array by keys <br>";
            krsort($people);
            displayAssociative($people);
        ?>

        <?php
            echo "<hr>";

            $Students = array
            (
            array("Mike",22,"Male", 75),
            array("Jason",18,"Male", 55),
            array("Jenny",25,"Female", 95),
            array("Megan",18,"Female", 85),
            array("Tom",34,"Male", 75),
            array("Lily",21,"Female", 75),
            array("Alex",18,"Male", 88),
            array("Amy",18,"Female", 75)
            );

            function display2D($A)
            {
                echo "<table border = 1>";
                echo "<tr> <th>Name</th> <th>Age</th> <th>Gender</th> <th>Grade</th> </tr>";
                foreach ($A as $s)
                {
                    echo "<tr>";
                        foreach ($s as $info)
                            echo "<td>".$info."</td>";
                    echo "</tr>";
                }

                echo "</table>";
            }

            echo "Original Data: <br>";
            display2D($Students);

            echo "<br>";

            echo "Sorted in ascending by name: <br>";
            sort($Students);
            display2D($Students);

            echo "Sorted in descending by name: <br>";
            rsort($Students);
            display2D($Students);

            echo "<br>";

            function cmpAgeASC($a, $b)
            {
                if ($a[1] == $b[1]) return 0; // means they are the same
                else if ($a[1] > $b[1]) return 1; // means $a is bigger than $b
                else return -1; // means $a is smaller than $b
            }

            function cmpAgeDESC($a, $b)
            {
                if ($a[1] == $b[1]) return 0;
                else if ($a[1] > $b[1]) return -1;
                else return 1;
            }

            echo "Sorted in ascending by age: <br>";
            usort($Students, "cmpAgeASC");
            display2D($Students);

            echo "Sorted in descending by age: <br>";
            usort($Students, "cmpAgeDESC");
            display2D($Students);

            echo "<br>";

            function cmpGenderASC($a, $b)
            {
                $index = 2;
                if ($a[$index] == $b[$index]) return 0;
                elseif ($a[$index] > $b[$index]) return 1;
                else return -1;
            }

            function cmpGenderDESC($a, $b)
            {
                return -cmpGenderASC($a, $b);
            }

            echo "Sorted in ascending by gender: <br>";
            usort($Students, "cmpGenderASC");
            display2D($Students);

            echo "Sorted in descending by gender: <br>";
            usort($Students, "cmpGenderDESC");
            display2D($Students);
        ?>

        <?php
            echo "<hr>";

            function cmpGradeName($a, $b)
            {
                $index1 = 3;
                $index2 = 0;

                if ($a[$index1] == $b[$index1]) // grade is the same
                {
                    if ($a[$index2] == $b[$index2]) // names are the same
                        return 0;
                    elseif ($a[$index2] > $b[$index2])
                        return 1;
                    else
                        return -1;
                }
                elseif ($a[$index1] > $b[$index1]) return -1;
                else return 1;
            }

            function cmpAgeGrade($a, $b)
            {
                $index1 = 1;
                $index2 = 3;

                if ($a[$index1] == $b[$index1])
                {
                    if ($a[$index2] == $b[$index2])
                        return 0;
                    elseif ($a[$index2] > $b[$index2])
                        return -1;
                    else
                        return 1;
                }
                elseif ($a[$index1] > $b[$index1]) return 1;
                else return -1;
            }

            echo "Sorted in descending by grade first, then in ascending by name <br>";
            usort($Students, "cmpGradeName");
            display2D($Students);

            echo "<br>";

            echo "Sorted in ascending by age first, then in descending by grade <br>";
            usort($Students, "cmpAgeGrade");
            display2D($Students);
        ?>

        <?php
            echo "<hr>";

            function cmpAgeGenderName($a, $b)
            {
                $index1 = 1;
                $index2 = 2;
                $index3 = 0;

                if ($a[$index1] == $b[$index1])
                {
                    if ($a[$index2] == $b[$index2])
                    {
                        if ($a[$index3] == $b[$index3]) return 0;
                        elseif ($a[$index3] > $b[$index3]) return -1;
                        else return 1;
                    }
                    elseif ($a[$index2] > $b[$index2])
                        return 1;
                    else
                        return -1;
                }
                elseif ($a[$index1] > $b[$index1]) return -1;
                else return 1;
            }

            echo "Sorted in descending by age, 
                    then in ascending by gender,
                    then in descending by name <br>";
            usort($Students, "cmpAgeGenderName");
            display2D($Students);
        ?>

    </body>
</html>
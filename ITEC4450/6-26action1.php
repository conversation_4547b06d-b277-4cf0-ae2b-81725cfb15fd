<?php
    session_start();
?>

<html>
    <head>
        <title>6-26</title>
    </head>

    <body>
        <?php
            if (isset($_POST["submit"]))
            {
                foreach ($_POST["want"] as $index=>$whichItem)
                {
                    if (isset($_SESSION["buy"]["$whichItem"]))
                    {
                        $_SESSION["buy"]["$whichItem"] = array($_POST["name"][$whichItem], $_POST["image"][$whichItem],
                            $_POST["price"][$whichItem], $_POST["amount"][$whichItem] + $_SESSION["buy"]["$whichItem"][3]);
                    }
                    else
                        $_SESSION["buy"]["$whichItem"] = array($_POST["name"][$whichItem], $_POST["image"][$whichItem],
                            $_POST["price"][$whichItem], $_POST["amount"][$whichItem]);
                }
            }

            $_SESSION["totalCost"] = 0;
            echo "<table>";
            echo "<tr>  <th>Item</th> <th>Price</th> <th>Quantity</th>  </tr>";
            echo "<tr>";
            foreach ($_SESSION["buy"] as $item)
            {
                $_SESSION["totalCost"] += $item[2]*$item[3];
                echo "<tr>";
                echo "<td> <img src='".$item[1]."' width='100' height='100'> </td>";
                echo "<td> $".$item[2]."</td>";
                echo "<td>".$item[3]."</td>";
                echo "</tr>";
            }
            echo "</tr>";
            echo "</table>";
            echo "The total amount is \$".$_SESSION["totalCost"]."<br>";

            echo "<a href='6-26action2.php'>Check Out</a> or <a href='6-26.php'>Continue Shopping</a>";
        ?>
    </body>
</html>